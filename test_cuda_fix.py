"""
测试CUDA上下文修复是否有效
"""

import os
# 设置环境变量来避免CUDA上下文警告
os.environ['CUDA_LAUNCH_BLOCKING'] = '1'

import torch
import torch.nn as nn
import warnings

# 捕获警告
warnings.filterwarnings('error')

def test_cuda_initialization():
    """测试CUDA初始化"""
    print("Testing CUDA initialization...")
    
    device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
    print(f"Device: {device}")
    
    if torch.cuda.is_available():
        # 强制初始化CUDA上下文
        torch.cuda.init()
        torch.cuda.set_device(0)
        
        # 创建张量并执行操作来完全初始化CUDA上下文
        dummy_tensor = torch.randn(10, 10, device=device, requires_grad=True)
        dummy_output = torch.matmul(dummy_tensor, dummy_tensor.t())
        dummy_loss = dummy_output.sum()
        dummy_loss.backward()  # 这会初始化cuBLAS上下文
        
        # 清理临时张量
        del dummy_tensor, dummy_output, dummy_loss
        torch.cuda.empty_cache()
        
        print("CUDA and cuBLAS initialized successfully!")
        return True
    else:
        print("CUDA not available")
        return False

def test_simple_network():
    """测试简单网络的前向和反向传播"""
    print("\nTesting simple network...")
    
    device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
    
    # 简单的网络
    class SimpleNet(nn.Module):
        def __init__(self):
            super().__init__()
            self.linear = nn.Linear(2, 1)
            
        def forward(self, x, t):
            return self.linear(torch.cat([x, t], dim=1))
    
    net = SimpleNet().to(device)
    
    # 测试数据
    x = torch.randn(5, 1, device=device, requires_grad=True)
    t = torch.randn(5, 1, device=device, requires_grad=True)
    
    try:
        # 前向传播
        output = net(x, t)
        
        # 计算梯度
        grad_outputs = torch.ones_like(output)
        grad_x = torch.autograd.grad(output, x, grad_outputs=grad_outputs, create_graph=True, allow_unused=True)[0]
        grad_t = torch.autograd.grad(output, t, grad_outputs=grad_outputs, create_graph=True, allow_unused=True)[0]

        # 二阶梯度
        if grad_x is not None:
            grad_xx = torch.autograd.grad(grad_x, x, grad_outputs=torch.ones_like(grad_x), create_graph=True, allow_unused=True)[0]
        else:
            grad_xx = torch.zeros_like(x)
        
        # 损失和反向传播
        loss = output.sum()
        if grad_xx is not None:
            loss = loss + grad_xx.sum()
        loss.backward()
        
        print("Network test passed - no CUDA warnings!")
        return True
        
    except Warning as w:
        print(f"Warning caught: {w}")
        return False
    except Exception as e:
        print(f"Error: {e}")
        return False

if __name__ == "__main__":
    print("=" * 50)
    print("CUDA Context Fix Test")
    print("=" * 50)
    
    # 测试CUDA初始化
    cuda_ok = test_cuda_initialization()
    
    # 测试网络
    if cuda_ok:
        network_ok = test_simple_network()
        
        if network_ok:
            print("\n✅ All tests passed! CUDA context warning should be fixed.")
        else:
            print("\n❌ Network test failed. Warning may still occur.")
    else:
        print("\n⚠️  CUDA not available, cannot test fix.")
    
    print("=" * 50)
