"""
测试修复后的PINN代码是否还有CUDA警告
"""

import os
# 设置环境变量来避免CUDA上下文警告
os.environ['CUDA_LAUNCH_BLOCKING'] = '1'

import torch
import torch.nn as nn
import numpy as np

# 设置设备并强制初始化CUDA上下文
device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
if torch.cuda.is_available():
    # 强制初始化CUDA上下文以避免cuBLAS警告
    torch.cuda.init()
    torch.cuda.set_device(0)
    
    # 创建张量并执行一些操作来完全初始化CUDA上下文
    dummy_tensor = torch.randn(10, 10, device=device, requires_grad=True)
    dummy_output = torch.matmul(dummy_tensor, dummy_tensor.t())
    dummy_loss = dummy_output.sum()
    dummy_loss.backward()  # 这会初始化cuBLAS上下文
    
    # 清理临时张量
    del dummy_tensor, dummy_output, dummy_loss
    torch.cuda.empty_cache()
    
    print(f"CUDA and cuBLAS initialized successfully. Using device: {device}")
    print(f"CUDA device name: {torch.cuda.get_device_name(0)}")
else:
    print(f"CUDA not available. Using device: {device}")

# 设置随机种子
sample_seed = 1024
torch.manual_seed(sample_seed)
np.random.seed(sample_seed)
if torch.cuda.is_available():
    torch.cuda.manual_seed(sample_seed)
    torch.cuda.manual_seed_all(sample_seed)

# 简化的PINN网络
class SimplePINN(nn.Module):
    def __init__(self, n_neurons=32, n_layers=4):
        super(SimplePINN, self).__init__()
        
        torch.manual_seed(sample_seed)
        np.random.seed(sample_seed)
        
        self.seq = torch.nn.Sequential()
        n_inputs = 2
        
        for i in range(n_layers):
            self.seq.add_module(f"lin_{i}", nn.Linear(n_inputs, n_neurons))
            self.seq.add_module(f"tanh_{i}", nn.Tanh())
            n_inputs = n_neurons
        
        self.seq.add_module(f"lin_{n_layers}", nn.Linear(n_inputs, 1))
        
        # 权重初始化
        for module in self.seq:
            if isinstance(module, nn.Linear):
                torch.nn.init.xavier_normal_(module.weight)
    
    def forward(self, x, t):
        return self.seq(torch.cat([x, t], axis=1))

def test_pinn_training():
    """测试PINN训练过程"""
    print("\nTesting PINN training process...")
    
    # 确保CUDA上下文在训练函数中也被正确设置
    if torch.cuda.is_available():
        torch.cuda.set_device(0)
        _ = torch.cuda.FloatTensor([1.0])
    
    # 创建模型
    pinn = SimplePINN().to(device)
    
    # 预热CUDA和cuBLAS以避免警告
    if torch.cuda.is_available():
        warmup_x = torch.randn(1, 1, device=device, requires_grad=True)
        warmup_t = torch.randn(1, 1, device=device, requires_grad=True)
        warmup_output = pinn(warmup_x, warmup_t)
        warmup_loss = warmup_output.sum()
        warmup_loss.backward()
        del warmup_x, warmup_t, warmup_output, warmup_loss
        torch.cuda.empty_cache()
    
    # 创建训练数据
    x_batch = torch.randn(100, 1, device=device, requires_grad=True)
    t_batch = torch.randn(100, 1, device=device, requires_grad=True)
    u_target = torch.randn(100, 1, device=device)
    
    # 可训练参数
    mu_pinn = torch.nn.Parameter(torch.tensor([4.0], device=device, requires_grad=True))
    eta_pinn = torch.nn.Parameter(torch.tensor([1.2], device=device, requires_grad=True))
    
    # 优化器
    optimizer = torch.optim.Adam(list(pinn.parameters()) + [mu_pinn, eta_pinn], lr=1e-3)
    mse_loss = torch.nn.MSELoss()
    
    print("Starting training loop...")
    
    # 训练几个步骤
    for epoch in range(5):
        if torch.cuda.is_available() and epoch == 0:
            torch.cuda.synchronize()
        
        # 前向传播
        net_u = pinn(x_batch, t_batch)
        
        # 计算梯度
        grad_outputs = torch.ones_like(net_u)
        pred_u_x = torch.autograd.grad(net_u, x_batch, grad_outputs=grad_outputs, create_graph=True)[0]
        pred_u_t = torch.autograd.grad(net_u, t_batch, grad_outputs=grad_outputs, create_graph=True)[0]
        pred_u_xx = torch.autograd.grad(pred_u_x, x_batch, grad_outputs=torch.ones_like(pred_u_x), create_graph=True)[0]
        pred_u_tt = torch.autograd.grad(pred_u_t, t_batch, grad_outputs=torch.ones_like(pred_u_t), create_graph=True)[0]
        pred_u_xx_t = torch.autograd.grad(pred_u_xx, t_batch, grad_outputs=torch.ones_like(pred_u_xx), create_graph=True)[0]
        
        # PDE损失
        pde_residual = pred_u_tt - mu_pinn * pred_u_xx - eta_pinn * pred_u_xx_t
        loss_pde = mse_loss(pde_residual, torch.zeros_like(pde_residual))
        
        # 数据损失
        loss_data = mse_loss(net_u, u_target)
        
        # 总损失
        loss = 0.2 * loss_data + 0.8 * loss_pde
        
        # 反向传播
        optimizer.zero_grad()
        loss.backward()
        optimizer.step()
        
        # CUDA同步
        if torch.cuda.is_available():
            torch.cuda.synchronize()
        
        print(f"Epoch {epoch+1}: Loss = {loss.item():.6f}, mu = {mu_pinn.item():.3f}, eta = {eta_pinn.item():.3f}")
    
    print("Training completed successfully - no CUDA warnings!")
    return True

if __name__ == "__main__":
    print("=" * 60)
    print("Testing PINN CUDA Context Fix")
    print("=" * 60)
    
    try:
        success = test_pinn_training()
        if success:
            print("\n✅ PINN training test passed! CUDA context warning should be fixed.")
        else:
            print("\n❌ PINN training test failed.")
    except Exception as e:
        print(f"\n❌ Error during testing: {e}")
    
    print("=" * 60)
