"""
复现文献的参数识别代码
PINN逆问题求解
"""

from tqdm import trange
import numpy as np
import torch
import torch.nn as nn
import matplotlib.pyplot as plt
from datetime import datetime, timezone, timedelta
import math
from torch.utils.data import DataLoader, TensorDataset

# 简单的CUDA初始化
device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
if torch.cuda.is_available():
    torch.cuda.empty_cache()  # 清理显存
    print(f"Using device: {device} - {torch.cuda.get_device_name(0)}")
else:
    print(f"Using device: {device}")


A=1
SIGMA = 1 # std of initial push [mm]
MU =4.0  # shear modulus [kPa]
ETA = 1.2 # viscosity [Pa.ms]

INITIAL_GUESS_MU = 8.0  #初始预测值，训练过程中作为可训练参数mu_pinn的初始值，通过迭代来优化，以接近真实的mu
INITIAL_GUESS_ETA = 3.0 #剪切粘度
CS = math.sqrt(MU)  # 剪切波速 [mm/ms]
RHO = 0.001 # 密度 [kg/mm^3]

RATIO = ETA
N_NEUROS = 32
N_LAYERS = 4
LR = 2e-3  # 学习率
x_range = [-20., 20.]    #[mm]
t_range = [0., 20.]      #[ms]

lambda1 = 0.2  # 数据损失的权重，衡量模型预测值与实际数据之间的差异
lambda2 = 0.8     # PDE损失的权重，该权重表示在总损失中 PDE 损失的占比
epochs = 20000 #超过12000就行

current_time = datetime.now(timezone(timedelta(hours=2))).strftime("%Y-%m-%d_%H-%M")    #获取当前时间

sample_seed = 1024
initialization_seed = 1024
torch.manual_seed(sample_seed)
np.random.seed(sample_seed)

# 如果使用CUDA，也设置CUDA的随机种子
if torch.cuda.is_available():
    torch.cuda.manual_seed(sample_seed)
    torch.cuda.manual_seed_all(sample_seed)

#定义位移方程求解函数
def get_displacement_wave_equation_output(
        u_init: np.array, u_init_2: np.array, DX: float, DT: float,
        NT: int, rho: float, mu: float, eta:float,  n_padding: int, x_values:np.array
):
    NX = len(u_init) + 2 * n_padding # 计算添加填充点后的总空间点数
    padding = np.zeros(n_padding)   #用于填充边界的零数组

    a = (mu * DT**2) / (rho * DX**2) #线性常数
    b = (eta * DT) / (rho * DX**2) #粘度系数

    u = np.zeros((NX, NT))  #创建(NX,NT)零数组,储存空间点在每个时间步的位移
    u[:, 0] = np.hstack((padding, u_init, padding)) #将初始位移u_iinit与填充数组拼接，赋值给u的第一列，即t=0时刻的位移

    #处理第二个时间步处位移
    if u_init_2 is None:
        for x in range(1, NX-1):    #若u_init_2为None，从0开始模拟，通过FDM计算第二个时间步处的位移
            u_linear = a * (u[x+1, 0] - 2 * u[x, 0] + u[x-1, 0])
            u_viscous = 0
            u[x, 1] = u_linear + u_viscous + u[x, 0]
    else:   #否则，使用之前模拟的结果作为第二个时间步时刻的位移
        u[:, 1] = np.hstack((padding, u_init_2, padding))

    for t in range(1, NT-1):    #迭代求解波动方程
        for x in range(1, NX-1):
            u_linear = a * (u[x+1, t] - 2 * u[x, t] + u[x-1, t])
            u_viscous = b *  (u[x+1, t] - 2 * u[x, t] + u[x-1, t] - u[x+1, t-1] + 2 * u[x, t-1] - u[x-1, t-1])
            u[x, t+1] = u_linear + u_viscous  + 2 * u[x, t] - u[x, t-1]

    plt.xlabel('x')
    plt.ylabel('u')
    for t in range(0, NT, 50):
        plt.plot(x_values, u[:, t])
    return u

# 定义高斯脉冲函数
def gaussian(x):
    return f_amp * np.exp(-x**2 / sigma**2)

#模拟位移波方程
# Tissue parameters
eta = ETA
mu = MU * 1e3      # Convert from mm/ms to m/s
rho = RHO * 1e6
n_padding = 0
#Simulation parameters for MU=1.5 4.0 7.0   ETA=0.5 1.2 2.0
DX = 1e-3
DT = 1e-4
NT = 200
# Spatial values
x_values = np.arange(-0.05, 0.05, DX)
# Parameters for Gaussian impulse
f_amp = 0.001
sigma = 0.001
u_init = np.zeros(len(x_values), dtype=np.float64)

for x in range(len(u_init)):
    u_init[x] = gaussian(x_values[x])   #对数组每个位置x调用高斯函数，结果赋给u_init
u_fdm = get_displacement_wave_equation_output(
    u_init=u_init, u_init_2=None, DX=DX, DT=DT, NT=NT, rho=rho, mu=mu, eta=eta, n_padding=n_padding, x_values=x_values
)
#调用函数，根据u_init等参数，求解波动方程，得到不同(x,t)处的粒子位移u_fdm



#FDM结果可视化
# FDM求解波动方程得到的位移结果
fig, ax = plt.subplots(figsize=(9, 6))
time_values = np.arange(
    0, u_fdm.shape[1] * DT * 1000, DT * 1000
)
# Find the indices corresponding to the desired positions
x_positions = [0.004, 0.008, 0.012, 0.016]
x_indices = [int((x - x_values[0]) / DX) for x in x_positions]
# Select the corresponding u values for each position
u_at_positions = [u_fdm[x_index, :] for x_index in x_indices]
# Plot the selected u values against time values for each position
for i, u_at_position in enumerate(u_at_positions):
    plt.plot(
        time_values, u_at_position * 1000, label=f"u(t, x={x_positions[i]*1000}mm)"
    )  # Convert meters to millimeters
plt.legend()
plt.title("FDM results without noise", fontsize=18)
plt.xlabel("Time [ms]", fontsize=16)
plt.ylabel("Displacement [mm]", fontsize=16)
plt.xticks(fontsize=14)
plt.yticks(fontsize=14)
plt.show()

# 用二维热图展示FDM求解波动方程的位移预测结果
fig, ax = plt.subplots(figsize=(9, 6))
spatial_values = np.arange(0, 0.02, DX) # 生成0到0.02步长为DX的空间值数组
spatial_values_mm = spatial_values * 1000  # 空间值从m转换为mm
u_fdm_2d = u_fdm * 1000 #位移值同上
# 定义时空范围并转换为索引
spatial_plot_range = 0.05
spatial_plot_start = 0  # Starting spatial position in meters
spatial_plot_end = 0.02  # Ending spatial position in meters
temporal_plot_end = 0.02  # Ending time in seconds
# Convert spatial and temporal ranges to indices
spatial_plot_start_index = int(
    (spatial_plot_start + spatial_plot_range) / DX
)  # Convert to index
spatial_plot_end_index = int((spatial_plot_end + spatial_plot_range) / DX)  # Convert to index
temporal_plot_end_index = int(temporal_plot_end / DT)  # Convert to index
# Define the temporal start index directly
temporal_plot_start_index = 0  # Start index for temporal range
# 从u_fdm_2d数组中切出所需的空间和时间范围的数据
u_fdm_2d_plot_sliced = u_fdm_2d[
    spatial_plot_start_index:spatial_plot_end_index, temporal_plot_start_index:temporal_plot_end_index
]
# 确定颜色映射范围
vmin = np.min(u_fdm_2d_plot_sliced)
vmax = np.max(u_fdm_2d_plot_sliced)
# Determine the absolute maximum value
abs_max = max(abs(vmin), abs(vmax))
# 绘制二维热图
heatmap = plt.imshow(
    u_fdm_2d_plot_sliced,
    extent=[
        0,0.02 * 1000,0,spatial_values_mm[-1],
],  # Convert seconds to milliseconds
    aspect="auto",cmap='coolwarm',vmin=-abs_max,vmax=abs_max,origin="lower",
)
plt.colorbar(label="Displacement [mm]")
plt.xlabel("Time [ms]")  # Change x-axis label to milliseconds
plt.ylabel("Lateral Position [mm]")  # Change y-axis label to millimeters
plt.title("FDM - 2D Prediction")
plt.show()

#u_fdm_2d_plot_sliced.shape

# 准备用于训练的数据
x_min = -0.02
x_max = 0.02
# 在x_values中找到第一个>=x_min和第一个>=x_max的元素的索引
start_index = np.argmax(x_values >= x_min)
end_index = np.argmax(x_values >= x_max)
# 提取出在[x_min,x_max)范围内的元素，并存储在x中
X = x_values[start_index:end_index]
# 提取时间步长，生成一个从0到u_fdm.shape[1]-1的整数数组
T = np.arange(u_fdm.shape[1]) * DT  # DT is the time step size
# 定义时空范围并转换为索引
spatial_range = 0.05
spatial_start = -0.02  # Starting spatial position in meters
spatial_end = 0.02  # Ending spatial position in meters
temporal_end = 0.02 # Ending time in seconds
temporal_range = 0.02

# 从u_fdm中提取出时空范围在上述范围内的数据，存储在u_fdm中
spatial_start_index = int((spatial_start + spatial_range) / DX)  # Convert to index
spatial_end_index = int((spatial_end + spatial_range) / DX)  # Convert to index
temporal_end_index = int((temporal_end ) / DT) + 1  # Convert to index
# Define the temporal start index directly
temporal_start_index = 0  # Start index for temporal range
# Slice the u_fdm_2d array to get the desired portion of data
u_fdm_without_noise = u_fdm[spatial_start_index:spatial_end_index, temporal_start_index:temporal_end_index]

X_train = X #将之前提取的X赋值给X_train，从T中提取出指定时间范围内的元素赋给T_train
T_train = T[temporal_start_index:temporal_end_index]
#noise_flag 是一个布尔变量，用于控制是否使用带噪声的数据进行训练
#如果noise_flag 为True，则使用带噪声的数据u_fdm_noisy 作为训练数据；否则，使用不带噪声的数据u_fdm_without_noise
u_train = u_fdm_without_noise

#打印训练数据的形状信息
print("Training set shapes:")
print("X_train shape:", X_train.shape)
print("T_train shape:", T_train.shape)
print("u_train shape:", u_train.shape)
#T_train[-1]
print("X_train shape:", X_train.shape)
print("X_train range: [{}, {}]".format(X_train.min(), X_train.max()))
print()
print("T_train shape:", T_train.shape)
print("T_train range: [{}, {}]".format(T_train.min(), T_train.max()))
print()
print("u_train shape:", u_train.shape)

#temporal_end_index
#T_train.shape


X_train *= 1000 # 对这三者的训练数据进行单位转换
T_train *= 1000
u_train *= 1000

# 找出数组中值为0的元素索引
zero_indices = np.where(T_train == 0)[0]
# 从T_train数组删除零值
T_train = np.delete(T_train, zero_indices, axis=0)
# 删除 u_train数组中与 T_train数组中零值对应的列，以保持数据的一致性
u_train = np.delete(u_train, zero_indices, axis=1)

print("Training set shapes:")
print("X_train shape:", X_train.shape)
print("T_train shape:", T_train.shape)
print("u_train shape:", u_train.shape)

print (X_train[1,])
print(T_train[1,])
print(u_train[1,1])

#绘制u_train热力图
# Assuming u_train is already defined
vmin = np.min(u_train)
vmax = np.max(u_train)
# Determine the absolute maximum value
abs_max = max(abs(vmin), abs(vmax))
# Plotting the heatmap for the training set
plt.figure(figsize=(8, 6))
plt.imshow(
    u_train,
    extent=[0, 20, -20, 20],
    aspect="auto",cmap="coolwarm",vmin=-abs_max,vmax=abs_max,origin="lower",
)
cbar = plt.colorbar()
cbar.set_label("Displacement [mm]", fontsize=16)
plt.xlabel("Time [ms]", fontsize=16)  # Change x-axis label to milliseconds
plt.ylabel("Lateral Position [mm]", fontsize=16)  # Change y-axis label to millimeters
plt.show()

#将数据转换为pytorch张量并移动到指定设备，然后打印张量形状信息
X_train = torch.tensor(X_train, dtype=torch.float32).to(device)
T_train = torch.tensor(T_train, dtype=torch.float32).to(device)
u_train = torch.tensor(u_train, dtype=torch.float32).to(device)
print("U Shape:", u_train.shape)
print("X" , X_train.shape)
print("T", T_train.shape)


#定义神经网络模型
class Net(nn.Module):

    def __init__(self, n_neurons=N_NEUROS, n_layers=N_LAYERS):
        super(Net, self).__init__()

        # Set the random seed
        torch.manual_seed(initialization_seed)
        np.random.seed(initialization_seed)
        self.seq = torch.nn.Sequential()
        n_inputs = 2

        for i in range(n_layers):
            self.seq.add_module(f"lin_{i}", nn.Linear(n_inputs, n_neurons))
            # self.seq.add_module(f"batch_norm_{i}", nn.BatchNorm1d(n_neurons))
            self.seq.add_module(f"tanh_{i}", nn.Tanh())
            n_inputs = n_neurons

        self.seq.add_module(f"lin_{n_layers}", nn.Linear(n_inputs, 1))
        # Set weight initialization method for each linear layer
        for module in self.seq:
            if isinstance(module, nn.Linear):
                torch.nn.init.xavier_normal_(module.weight)

    def forward(self, x, t):
        return self.seq(torch.cat([x, t], axis=1))

print("U Shape:", u_train.shape)
print("X" , X_train.shape)
print("T", T_train.shape)

#创建数据加载器
pairs = []
pairs_u = []
#把X_train,T_train两两组合成数据对并将对应的位移值收集起来，for生成数据对，创建一个完整的数据集
for i in range (len(X_train)):
    for j in range (len(T_train)):
        pairs.append([X_train[i], T_train[j]])
        pairs_u.append(u_train[i,j])
# Convert pairs and pairs_u to tensors
pairs_tensor = torch.tensor(pairs)
pairs_u_tensor = torch.tensor(pairs_u)

# 将上述两个张量合成一个数据集
dataset = TensorDataset(pairs_tensor, pairs_u_tensor)
# 设置更大的批量大小来充分利用GPU
batch_size = min(4096, len(X_train) * len(T_train))  # 使用4096或全部数据，取较小值
# 创建数据加载器，shuffle=True在每个训练周期开始时打乱数据的顺序
dataloader = DataLoader(dataset, batch_size=batch_size, shuffle=True, pin_memory=True, num_workers=0)

#定义训练函数
def train_model(dataloader):
    mu_pinn = torch.nn.Parameter(
        torch.tensor([INITIAL_GUESS_MU], requires_grad=True, dtype=torch.float32).to(device)
    )   #torch.nn.Parameter 用于将张量标记为可训练参数，使得在反向传播过程中可以更新这些参数的值
    eta_pinn = torch.nn.Parameter(
        torch.tensor([INITIAL_GUESS_ETA], requires_grad=True, dtype=torch.float32).to(device)
    )
    print("Te real mu = [", MU, "], eta = [", ETA, "]") #打印材料的真实剪切模量与粘度模量

    pinn = Net(n_neurons=N_NEUROS, n_layers=N_LAYERS)
    pinn = pinn.to(device)
    # 定义损失函数和优化器
    mse_loss = torch.nn.MSELoss()
    adam_optimizer = torch.optim.Adam(
        list(pinn.parameters()) + [mu_pinn] + [eta_pinn], lr=LR
    )

    mu_s = []   #初始化记录列表
    eta_s = []
    epochs_list = []
    losses = [[], []]
    # 初始化参数历史记录列表
    mu_history = []
    eta_history = []
    # 设置收敛标准
    convergence_threshold = 1e-6
    consecutive_epochs = 1000

    for epoch in (pbar := trange(epochs)):
        # mu_history 和eta_history 是两个列表，分别用于记录每个训练周期中mu_pinn 和eta_pinn 的值
        mu_history.append(mu_pinn.item())
        eta_history.append(eta_pinn.item())

        # 检查收敛条件，consecutive_epochs 是一个预设的整数，表示在经过多少个训练周期后开始检查收敛条件
        # 只有当当前训练周期数大于等于 consecutive_epochs 时，才会进行收敛检查
        if epoch >= consecutive_epochs:
            recent_mu = mu_history[-consecutive_epochs:]
            recent_eta = eta_history[-consecutive_epochs:]
            #recent_mu 和recent_eta分别是mu_history 和eta_history列表中最近consecutive_epochs个元素组成的子列表
            # 分别计算两个子列表的标准差，反映mu_pinn 和 eta_pinn 在最近 consecutive_epochs 个训练周期内的波动情况
            mu_std = np.std(recent_mu)
            eta_std = np.std(recent_eta)

            # 判断是否收敛
            if mu_std < convergence_threshold and eta_std < convergence_threshold:
                print(f"Mu and eta have converged after {consecutive_epochs} epochs.")
                break

        epochs_list.append(epoch)   #记录训练周期数

        for batch_data, batch_target in dataloader:
            x_batch = batch_data[:, 0].unsqueeze(1)
            t_batch = batch_data[:, 1].unsqueeze(1)
            u_train_batch = batch_target.unsqueeze(1)

        x_batch = x_batch.to(device).requires_grad_(True).float()
        t_batch = t_batch.to(device).requires_grad_(True).float()

        u_train_batch = u_train_batch.to(device).float()
        net_u = pinn(x_batch, t_batch).to(device)

        pred_u_x = torch.autograd.grad(
            net_u, x_batch, grad_outputs=torch.ones_like(net_u), create_graph=True,
        )[0]
        pred_u_t = torch.autograd.grad(
            net_u, t_batch, grad_outputs=torch.ones_like(net_u), create_graph=True,
        )[0]
        pred_u_xx = torch.autograd.grad(
            pred_u_x, x_batch, grad_outputs=torch.ones_like(pred_u_x), create_graph=True,
        )[0]
        pred_u_tt = torch.autograd.grad(
            pred_u_t, t_batch, grad_outputs=torch.ones_like(pred_u_t), create_graph=True,
        )[0]
        pred_u_xx_t = torch.autograd.grad(
            pred_u_xx, t_batch, grad_outputs=torch.ones_like(pred_u_xx), create_graph=True,
        )[0]

        # PDE loss - 确保所有张量在同一设备上
        pde_residual = pred_u_tt - mu_pinn * pred_u_xx - eta_pinn * pred_u_xx_t
        loss_pde = mse_loss(pde_residual, torch.zeros_like(pde_residual))

        # data loss
        loss_data = mse_loss(net_u, u_train_batch)

        # Combining the loss functions
        # loss = lambda1 * loss_data + lambda2 * loss_pde + lambda3* loss_ic_u + lambda4 * loss_ic_dudt
        loss = lambda1 * loss_data + lambda2 * loss_pde

        loss.backward()
        adam_optimizer.step()
        adam_optimizer.zero_grad()

        # 如果使用CUDA，同步以确保操作完成
        if torch.cuda.is_available():
            torch.cuda.synchronize()

        losses[0].append(loss_pde.item())
        losses[1].append(loss_data.item())

        mu_s.append(mu_pinn.item())
        eta_s.append(eta_pinn.item())

        pbar.set_description(
            f"Loss={loss.item():.3e}, mu_pinn={mu_pinn.item():.3e}, eta_pinn={eta_pinn.item():.3e}"
        )
    return losses, mu_s, eta_s, pinn

#训练模型
# 使用torch.cuda.device上下文管理器确保正确的CUDA设备管理
if torch.cuda.is_available():
    with torch.cuda.device(device):
        losses, mu_s, eta_s, pinn = train_model(dataloader)
else:
    losses, mu_s, eta_s, pinn = train_model(dataloader)

#绘制训练结果图
# Plot for mu
fig, ax = plt.subplots()
plt.title(r"$\mu$")
plt.plot(mu_s, label="PINN estimate")
plt.hlines(MU, 0, len(mu_s), label="True value", color="tab:green")
plt.legend()
plt.xlabel("Training step")

# Plot for eta
fig, ax = plt.subplots()
plt.title(r"$\eta$")
plt.plot(eta_s, label="PINN estimate")
plt.hlines(ETA, 0, len(eta_s), label="True value", color="tab:green")
plt.legend()
plt.xlabel("Training step")

fig, (ax1, ax2) = plt.subplots(1, 2, figsize=(10, 5))
# Plotting Total Loss
total_loss = [
    losses[0][i] + losses[1][i] for i in range(min(len(losses[0]), len(losses[1])))
]
ax2.plot(total_loss, label="Total Loss")
ax2.set_yscale("log")
ax2.legend()
ax2.set_title("Total Loss")

ln1 = ax1.plot(losses[0], label="PDE - loss")
ln2 = ax1.plot(losses[1], label="Data- loss")
ax1.set_yscale("log")
lns = ln1 + ln2
labs = [l.get_label() for l in lns]
ax1.legend(lns, labs, loc=0)
ax1.set_title("Two Losses")


fig, (ax1, ax2) = plt.subplots(1, 2, figsize=(10, 5))
# Plotting Total Loss
total_loss = [
    losses[0][i] + losses[1][i] for i in range(min(len(losses[0]), len(losses[1])))
]
ax2.plot(total_loss, label="Total Loss")
ax2.set_yscale("log")
ax2.legend()
ax2.set_title("Total Loss")

ln1 = ax1.plot(losses[0], label="PDE - loss")
ln2 = ax1.plot(losses[1], label="Data- loss")
ax1.set_yscale("log")

lns = ln1 + ln2
labs = [l.get_label() for l in lns]
ax1.legend(lns, labs, loc=0)
ax1.set_title("Two Losses")
plt.show()

#绘制PINN训练结果图
selected_x_values = [4, 8, 12, 16]
num_test_samples = 500
# Set the model to evaluation mode
pinn.eval()
# Create a single figure
fig, ax = plt.subplots()
# Plot u(t, x) for each selected x on the same graph
# Plot u(t, x) for each selected x on the same graph
for selected_x in selected_x_values:
    # Predict u(t, x) distribution for the fixed x
    t_flat = np.linspace(0, 20, num_test_samples)
    x_flat = np.full_like(t_flat, fill_value=selected_x)
    tx = np.stack([t_flat, x_flat], axis=-1)

    # Convert to torch tensors
    x_tensor = torch.from_numpy(tx[:, 1]).float().view(-1, 1).to(device)
    t_tensor = torch.from_numpy(tx[:, 0]).float().view(-1, 1).to(device)

    # Forward pass
    u_pinn = pinn(x_tensor, t_tensor).detach()
    # Move predicted values to CPU for plotting
    u_pinn = u_pinn.cpu().numpy()

    # Plot the result
    ax.plot(t_flat, u_pinn, label=f'u(t, x={selected_x}mm)')

ax.set_title("PINN - Prediction")
ax.set_xlabel("Time [ms]")
ax.set_ylabel("Displacement [mm]")
ax.legend()
plt.show()
